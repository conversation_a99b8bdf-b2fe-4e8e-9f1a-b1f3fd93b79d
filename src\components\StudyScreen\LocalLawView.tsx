import React, { useState } from 'react';
import { Virtual<PERSON>ei } from '../VirtualLei';
import { ReapplyHighlights } from '../ReapplyHighlights';
import { getNodeFromPath } from '../../hooks/useTextSelection';
import { useLawContext } from '../../hooks/useLawContext';
import { useAnnotations } from '../../hooks/useAnnotations';
import { useArticleClick } from '../../hooks/useArticleClick';
import { useAnnotationIndicators } from '../../hooks/useAnnotationIndicators';
import { TableOfContents } from './TableOfContents';
import { useHierarchicalNavToggle } from '../../hooks/useHierarchicalNavToggle';
import { HierarchicalNavigation } from './HierarchicalNavigation';
import { AnnotationModal } from './AnnotationModal';
import Modal from '../ui/Modal';
import { ProfileScreen } from '../../pages/ProfileScreen';
import { LawHeader } from './LawHeader';
import { PaywallModal } from './PaywallModal';
import { ArticleModal } from './ArticleModal';
import type { LocalLawViewProps } from '../../types/lawView';

export function LocalLawView({
  containerRef,
  leiMeta,
  navigate,
  corAtiva,
  setCorAtiva,
  diminuir,
  aumentar,
  exportarGrifos,
  importarGrifos,
  removeHighlight,
  virtualRef,
  visibleArtigos,
  handleScroll,
  highlights,
  tocItems,
  showPaywall,
  handlePaywallDismiss,
  creditsLeft,
  dialogArt,
  closeDialog,
  loadingExp,
  handleExplain,
  handleExplainWithExample,
  loadingJur,
  handleJurisprudence,
  dialogExp,
  dialogJur,
  formatIaText,
}: LocalLawViewProps) {
  // Get hierarchical context for local laws
  const { context: hierarchicalContext, debugInfo } = useLawContext(containerRef || { current: null }, [visibleArtigos]);

  // Hierarchical navigation toggle state
  const { isVisible: isHierarchicalNavVisible, toggle: toggleHierarchicalNav } = useHierarchicalNavToggle();

  // Profile modal state
  const [showProfileModal, setShowProfileModal] = useState(false);

  // Sistema de anotações
  const leiId = leiMeta.id;
  const {
    annotations,
    getAnnotation,
    saveAnnotation,
    generateWithAI,
    hasAnnotation,
  } = useAnnotations(leiId);

  // Estado do modal de anotações
  const [annotationModal, setAnnotationModal] = useState<{
    isOpen: boolean;
    articleId: string;
    articleText: string;
  }>({
    isOpen: false,
    articleId: '',
    articleText: '',
  });

  // Manipula clique nos artigos
  const handleArticleClick = (articleId: string, articleText: string) => {
    setAnnotationModal({
      isOpen: true,
      articleId,
      articleText,
    });
  };

  // Hook para detectar cliques nos artigos
  useArticleClick({
    containerRef,
    onArticleClick: handleArticleClick,
    dependency: visibleArtigos,
  });

  // Hook para mostrar indicadores visuais de anotações
  useAnnotationIndicators({
    containerRef,
    annotations,
    dependency: visibleArtigos,
  });

  // Funções para o modal de anotações
  const handleSaveAnnotation = async (data: any) => {
    await saveAnnotation(annotationModal.articleId, data);
  };

  const handleGenerateAI = async (options: any) => {
    return await generateWithAI(annotationModal.articleText, options);
  };

  const closeAnnotationModal = () => {
    setAnnotationModal({
      isOpen: false,
      articleId: '',
      articleText: '',
    });
  };

  return (
    <div ref={containerRef as any} id="lei-container" className="h-screen flex flex-col scroll-auto" onScroll={handleScroll}>
      <LawHeader
        leiMeta={leiMeta}
        navigate={navigate}
        corAtiva={corAtiva}
        setCorAtiva={setCorAtiva}
        diminuir={diminuir}
        aumentar={aumentar}
        exportarGrifos={exportarGrifos}
        importarGrifos={importarGrifos}
        toggleHierarchicalNav={toggleHierarchicalNav}
        onProfileClick={() => setShowProfileModal(true)}
        isHierarchicalNavVisible={isHierarchicalNavVisible}
        hasHierarchicalContext={!!hierarchicalContext}
      />

      <VirtualLei
        ref={virtualRef}
        artigos={visibleArtigos.map(a => a.texto)}
        onAnnotationClick={handleArticleClick}
        removeHighlight={removeHighlight}
      />

      {/* ReapplyHighlights component for local laws - target the entire container */}
      {highlights.length > 0 && (
        <ReapplyHighlights
          highlights={highlights}
          containerId="lei-container"
          getNodeFromPath={getNodeFromPath}
          onRemove={(id) => removeHighlight(id)}
        />
      )}

      <PaywallModal
        isVisible={showPaywall}
        creditsLeft={creditsLeft}
        navigate={navigate}
        onDismiss={handlePaywallDismiss}
      />

      <ArticleModal
        dialogArt={dialogArt}
        onClose={closeDialog}
        loadingExp={loadingExp}
        loadingJur={loadingJur}
        onExplain={handleExplain}
        onExplainWithExample={handleExplainWithExample}
        onJurisprudence={handleJurisprudence}
        dialogExp={dialogExp}
        dialogJur={dialogJur}
        formatIaText={formatIaText}
      />

      <TableOfContents
        tocItems={tocItems}
        onSelect={(id) => {
          const index = visibleArtigos.findIndex(a => a.id === id);
          if (index !== -1) {
            virtualRef.current?.scrollTo(index);
          }
        }}
      />

      {/* Enhanced Hierarchical Navigation */}
      {hierarchicalContext && isHierarchicalNavVisible && (
        <HierarchicalNavigation
          context={hierarchicalContext}
          debugInfo={debugInfo}
          onHide={toggleHierarchicalNav}
        />
      )}

      {/* Profile Modal */}
      <Modal isOpen={showProfileModal} onClose={() => setShowProfileModal(false)} size="lg">
        <ProfileScreen minimal={true} onClose={() => setShowProfileModal(false)} />
      </Modal>

      {/* Annotation Modal */}
      <AnnotationModal
        isOpen={annotationModal.isOpen}
        onClose={closeAnnotationModal}
        articleId={annotationModal.articleId}
        articleText={annotationModal.articleText}
        leiId={leiId}
        annotation={annotations.get(annotationModal.articleId)}
        onSave={handleSaveAnnotation}
        onGenerateAI={handleGenerateAI}
      />
    </div>
  );
};
