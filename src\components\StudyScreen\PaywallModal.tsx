import React, { useState } from 'react';
import { GoogleAuthProvider, signInWithPopup, signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../../firebase';
import toast from 'react-hot-toast';
import type { PaywallModalProps } from '../../types/lawView';

export const PaywallModal: React.FC<PaywallModalProps> = ({
  isVisible,
  creditsLeft,
  navigate,
  onDismiss,
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isRegistering, setIsRegistering] = useState(false);
  const [loading, setLoading] = useState(false);

  if (!isVisible) return null;

  const handleGoogle = async () => {
    if (!auth) return toast.error('Firebase não configurado');
    try {
      setLoading(true);
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
      toast.success('Login realizado!');
      onDismiss();
    } catch (err: any) {
      toast.error(err.message || 'Falha no login');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailPass = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!auth) return toast.error('Firebase não configurado');
    try {
      setLoading(true);
      if (isRegistering) {
        await createUserWithEmailAndPassword(auth, email, password);
        toast.success('Conta criada!');
      } else {
        await signInWithEmailAndPassword(auth, email, password);
        toast.success('Login realizado!');
      }
      onDismiss();
    } catch (err: any) {
      const code = err.code as string | undefined;
      if (code === 'auth/admin-restricted-operation') {
        toast.error('Cadastro desativado. Contate o administrador.');
      } else {
        toast.error(err.message || 'Falha no login');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubscription = (priceId: string) => {
    // Redireciona para a página de login com o plano selecionado
    navigate(`/login?plan=${priceId}`);
  };

  return (
    <div className="fixed inset-0 bg-neutral-900/80 backdrop-blur-md z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-neutral-900 rounded-3xl shadow-2xl max-w-sm w-full animate-scaleIn border border-neutral-200 dark:border-neutral-800">
        {/* Header */}
        <div className="p-8 text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl mx-auto mb-6 flex items-center justify-center">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-white mb-2">Acesso Premium</h2>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            {creditsLeft > 0
              ? `${creditsLeft} créditos restantes`
              : 'Créditos esgotados'
            }
          </p>
        </div>

        {/* Login Form */}
        <div className="px-8 pb-8 space-y-4">
          <button
            onClick={handleGoogle}
            disabled={loading}
            className="w-full py-3 px-4 rounded-xl bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 text-neutral-700 dark:text-neutral-300 shadow-sm hover:shadow-md flex items-center justify-center gap-3 transition-all duration-200 font-medium"
          >
            <img
              src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"
              alt="Google"
              className="w-5 h-5"
            />
            Continuar com Google
          </button>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-neutral-200 dark:border-neutral-700"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white dark:bg-neutral-900 text-neutral-500">ou</span>
            </div>
          </div>

          <form onSubmit={handleEmailPass} className="space-y-3">
            <input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="input-modern"
              required
            />
            <input
              type="password"
              placeholder="Senha"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="input-modern"
              required
            />
            <button
              type="submit"
              disabled={loading}
              className="btn-primary w-full"
            >
              {isRegistering ? 'Criar Conta' : 'Entrar'}
            </button>
          </form>

          <button
            onClick={() => setIsRegistering(!isRegistering)}
            className="w-full text-sm text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white transition-colors"
          >
            {isRegistering ? 'Já tem conta? Entrar' : 'Criar nova conta'}
          </button>

          <div className="pt-4 border-t border-neutral-200 dark:border-neutral-700">
            <button
              onClick={onDismiss}
              className="w-full text-sm text-neutral-500 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors"
            >
              Continuar sem conta
            </button>
          </div>
        </div>

        {/* Call to Action */}
        <div className="px-8 pb-8">
          <div className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950 dark:to-accent-950 rounded-2xl p-6 text-center border border-primary-100 dark:border-primary-900">
            <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-2">Acesso Ilimitado</h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
              Grifos salvos, IA ilimitada e sincronização em todos os dispositivos
            </p>
            <button
              onClick={() => navigate('/login')}
              className="btn-primary w-full"
            >
              Ver Planos
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
