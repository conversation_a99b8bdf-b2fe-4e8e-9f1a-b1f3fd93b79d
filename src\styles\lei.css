#lei-container {
  line-height: 1.65;
  font-size: var(--fs, 1rem);
  width: 100%;
  max-width: 100vw;
  margin: 0;
  padding-left: 0;
  padding-right: 0;
  box-sizing: border-box;
  white-space: normal;
  text-align: justify;
}

@media (min-width: 601px) {
  #lei-container {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding-left: 0.8cm;
    padding-right: 0.8cm;
  }
}

#lei-container p {
  margin-bottom: 1.2em;
  text-align: left;
}

/* Otimização para evitar flash de títulos não processados */
#lei-container p {
  transition: opacity 0.15s ease-in-out;
}

/* Classe temporária para títulos que estão sendo processados */
#lei-container p.processing-title {
  opacity: 0.4;
  transform: scale(0.98);
  transition: all 0.15s ease-in-out;
}
#lei-container p.heading {
  background: rgba(251, 191, 36, 0.08) !important;
  border-left: 4px solid #fde68a !important;
  padding: 0.5em 1em !important;
  margin-top: 2em !important;
  margin-bottom: 1.2em !important;
  border-radius: 0.4em !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 1px 4px 0 rgba(251, 191, 36, 0.04) !important;
  text-align: left !important;
  display: block !important;
  line-height: 1.4 !important;
}
#lei-container p.art {
  margin-left: 0;
}
#lei-container p.para,
#lei-container p.inciso {
  margin-left: 0;
}
#lei-container p.note {
  font-style: normal;
  font-size: 0.85em;
  color: #60a5fa; /* azul claro */
}
#lei-container p.note .lei-ref {
  color: #3b82f6; /* azul mais forte para referencias */
}
mark[data-grifo] {
  position: relative;
  cursor: pointer;
  display: inline;
  pointer-events: auto;
  user-select: none;
}
mark[data-grifo]:hover::after {
  content: "✕";
  position: absolute;
  right: 2px;
  top: -2px;
  color: #f87171;
  font-weight: bold;
}
/* Forçar uniformidade de fonte */
#lei-container,
#lei-container * {
  font-size: inherit !important;
  font-style: normal !important;
}

.prose {
  font-size: var(--fs, 1rem);
}

.note-inline {
  font-size: 0.75em;
  color: #60a5fa;
}

.art-ref {
  color: #93c5fd;
  cursor: pointer;
  text-decoration: underline dotted;
}

/* Botão elegante exibido somente no hover do item */
.art-head-btn {
  background: transparent;
  border: none;
  padding: 0 0.25em;
  cursor: pointer;
  color: #fbbf24 !important;
  font-size: 0.9em;
  line-height: 1;
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.2s ease, transform 0.2s ease, color 0.2s ease;
  position: relative;
}

.art-head-btn::before {
  content: "⋮"; /* três pontos verticais */
  display: inline-block;
}

/* Exibe o botão quando o usuário passa o mouse sobre o parágrafo do artigo */
p.art:hover .art-head-btn,
.art-head-btn:focus {
  opacity: 1;
  transform: scale(1);
}

.art-head-btn:hover {
  color: #fde68a !important;
}

/* Indicador visual para artigos com anotações - removido do cabeçalho */

.art-head-btn[data-has-annotation="true"] {
  color: #10b981 !important;
  opacity: 1;
  transform: scale(1);
}

/* Botão de ações fica verde quando há anotações, sem ícone adicional */

/* Indicador visual discreto no final do texto do artigo */
.annotation-indicator {
  display: inline;
  margin-left: 0.4em;
}

.annotation-badge {
  display: inline-block;
  font-size: 0.75em;
  opacity: 0.6;
  background: rgba(16, 185, 129, 0.08);
  padding: 0.15em 0.4em;
  border-radius: 0.3em;
  border: 1px solid rgba(16, 185, 129, 0.2);
  transition: opacity 0.2s ease, transform 0.15s ease, background 0.2s ease;
  cursor: pointer;
}

.annotation-badge:hover {
  opacity: 0.9;
  transform: scale(1.05);
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.4);
}

/* Cabeçalhos de artigo – efeito sutil clicável */
#lei-container .art-heading {
  color: inherit !important; /* neutral baseline */
  font-weight: 700 !important;
  cursor: pointer;
  position: relative;
  transition: color 0.15s ease;
  text-decoration: none;
}
#lei-container .art-heading:hover {
  color: #fde68a !important; /* âmbar claro */
}
#lei-container .art-heading::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 100%;
  height: 1px;
  background: currentColor;
  opacity: 0.5;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.2s ease;
}
#lei-container .art-heading:hover::after {
  transform: scaleX(1);
}

/* Links dentro do texto da lei */
#lei-container a {
  color: #60a5fa !important; /* azul padrão */
  text-decoration: underline;
}
#lei-container a:hover {
  color: #93c5fd !important;
}

/* Virtualização nativa do Chrome/Edge – torna elementos fora da viewport baratos */
#lei-container > * {
  content-visibility: auto;
  contain-intrinsic-size: 1000px 0;
}

/* Destaque para Livros, Títulos, Capítulos e Seções */
#lei-container p.livro, 
#lei-container p.titulo, 
#lei-container p.capitulo, 
#lei-container p.secao,
#lei-container p.livro.heading,
#lei-container p.titulo.heading,
#lei-container p.capitulo.heading,
#lei-container p.secao.heading {
  background: rgba(251, 191, 36, 0.08) !important;
  border-left: 4px solid #fde68a !important;
  padding: 0.5em 1em !important;
  margin-top: 2em !important;
  margin-bottom: 1.2em !important;
  border-radius: 0.4em !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 1px 4px 0 rgba(251, 191, 36, 0.04) !important;
  text-align: left !important;
}

/* Itens flutuantes dentro desses blocos (ex: botões, ícones) */
#lei-container p.livro .float-item,
#lei-container p.titulo .float-item,
#lei-container p.capitulo .float-item,
#lei-container p.secao .float-item {
  float: right;
  margin-left: 1em;
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.13);
  border-radius: 0.3em;
  padding: 0.1em 0.5em;
  font-size: 0.95em;
  font-weight: 500;
  box-shadow: 0 1px 2px 0 rgba(251, 191, 36, 0.08);
  transition: background 0.2s, color 0.2s;
}
#lei-container p.livro .float-item:hover,
#lei-container p.titulo .float-item:hover,
#lei-container p.capitulo .float-item:hover,
#lei-container p.secao .float-item:hover {
  background: #fde68a;
  color: #92400e;
}

@media (min-width: 601px) {
  #lei-container p.art {
    margin-left: 2rem;
  }
  #lei-container p.para,
  #lei-container p.inciso {
    margin-left: 1.5rem;
  }
}

/* Estilos clean e profissionais para títulos combinados */
#lei-container p.combined-title {
  background: rgba(15, 23, 42, 0.92) !important;
  border: none !important;
  border-left: 3px solid #3b82f6 !important;
  padding: 0.7rem 1rem !important;
  margin: 1.5rem 0 1rem 0 !important;
  border-radius: 4px !important;
  font-weight: 600 !important;
  font-size: 0.95em !important;
  letter-spacing: 0.3px !important;
  text-transform: uppercase !important;
  color: #f1f5f9 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
  text-align: left !important;
  line-height: 1.3 !important;
  position: relative !important;
}

/* Cores específicas por hierarquia - Especificidade aumentada */
#lei-container p.combined-title.title-livro {
  border-left-color: #ef4444 !important;
  background: rgba(127, 29, 29, 0.85) !important;
  border-color: #ef4444 !important;
}

#lei-container p.combined-title.title-titulo {
  border-left-color: #3b82f6 !important;
  background: rgba(30, 58, 138, 0.85) !important;
  border-color: #3b82f6 !important;
}

#lei-container p.combined-title.title-capitulo {
  border-left-color: #10b981 !important;
  background: rgba(6, 78, 59, 0.85) !important;
  border-color: #10b981 !important;
}

#lei-container p.combined-title.title-secao {
  border-left-color: #f59e0b !important;
  background: rgba(146, 64, 14, 0.85) !important;
  border-color: #f59e0b !important;
}

#lei-container p.combined-title.title-parte {
  border-left-color: #8b5cf6 !important;
  background: rgba(91, 33, 182, 0.85) !important;
  border-color: #8b5cf6 !important;
  margin-left: 6rem !important;
}

#lei-container p.combined-title.title-default {
  border-left-color: #6b7280 !important;
  background: rgba(55, 65, 81, 0.85) !important;
  border-color: #6b7280 !important;
}

/* Pontinhos verticais para guias hierárquicas - sistema simplificado */
#lei-container {
  position: relative;
}

/* Container para linhas de guia hierárquica */
#lei-container p.combined-title {
  position: relative;
}

/* Linha pontilhada para TÍTULO (nível 1) */
#lei-container p.combined-title.title-titulo::before {
  content: "";
  position: absolute;
  left: -1.5rem;
  top: -1rem;
  bottom: -1rem;
  width: 1px;
  background: repeating-linear-gradient(
    to bottom,
    transparent 0px,
    transparent 3px,
    rgba(59, 130, 246, 0.4) 3px,
    rgba(59, 130, 246, 0.4) 6px
  );
}

/* Linhas pontilhadas para CAPÍTULO (nível 2) */
#lei-container p.combined-title.title-capitulo::before {
  content: "";
  position: absolute;
  left: -3rem;
  top: -1rem;
  bottom: -1rem;
  width: 1px;
  background: repeating-linear-gradient(
    to bottom,
    transparent 0px,
    transparent 3px,
    rgba(59, 130, 246, 0.4) 3px,
    rgba(59, 130, 246, 0.4) 6px
  );
  box-shadow: 1.5rem 0 0 0 rgba(16, 185, 129, 0.4);
  background-image:
    repeating-linear-gradient(
      to bottom,
      transparent 0px,
      transparent 3px,
      rgba(59, 130, 246, 0.4) 3px,
      rgba(59, 130, 246, 0.4) 6px
    ),
    repeating-linear-gradient(
      to bottom,
      transparent 0px,
      transparent 3px,
      rgba(16, 185, 129, 0.4) 3px,
      rgba(16, 185, 129, 0.4) 6px
    );
  background-position: 0 0, 1.5rem 0;
  background-size: 1px 100%, 1px 100%;
  background-repeat: repeat-y, repeat-y;
}

/* Linhas pontilhadas para SEÇÃO (nível 3) */
#lei-container p.combined-title.title-secao::before {
  content: "";
  position: absolute;
  left: -4.5rem;
  top: -1rem;
  bottom: -1rem;
  width: 1px;
  background-image:
    repeating-linear-gradient(
      to bottom,
      transparent 0px,
      transparent 3px,
      rgba(59, 130, 246, 0.4) 3px,
      rgba(59, 130, 246, 0.4) 6px
    ),
    repeating-linear-gradient(
      to bottom,
      transparent 0px,
      transparent 3px,
      rgba(16, 185, 129, 0.4) 3px,
      rgba(16, 185, 129, 0.4) 6px
    ),
    repeating-linear-gradient(
      to bottom,
      transparent 0px,
      transparent 3px,
      rgba(245, 158, 11, 0.4) 3px,
      rgba(245, 158, 11, 0.4) 6px
    );
  background-position: 0 0, 1.5rem 0, 3rem 0;
  background-size: 1px 100%, 1px 100%, 1px 100%;
  background-repeat: repeat-y, repeat-y, repeat-y;
}

/* Responsividade para mobile */
@media (max-width: 600px) {
  #lei-container p.combined-title {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.9em !important;
    margin: 1rem 0 0.8rem 0 !important;
  }
}
