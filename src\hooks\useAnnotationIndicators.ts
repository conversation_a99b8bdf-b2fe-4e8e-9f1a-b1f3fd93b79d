import { useEffect, RefObject } from 'react';
import type { ArticleAnnotation } from '../types/annotations';

interface UseAnnotationIndicatorsProps {
  containerRef: RefObject<HTMLElement>;
  annotations: Map<string, ArticleAnnotation>;
  dependency?: any;
}

export function useAnnotationIndicators({
  containerRef,
  annotations,
  dependency,
}: UseAnnotationIndicatorsProps) {

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Remove todos os indicadores existentes
    const existingIndicators = container.querySelectorAll('[data-has-annotation], .annotation-indicator');
    existingIndicators.forEach(el => {
      if (el.classList.contains('annotation-indicator')) {
        el.remove();
      } else {
        el.removeAttribute('data-has-annotation');
      }
    });

    // Adiciona indicadores para artigos com anotações
    annotations.forEach((annotation, articleId) => {
      const hasContent = !!(
        annotation.explicacao?.trim() ||
        annotation.doutrina?.trim() ||
        annotation.jurisprudencia?.trim()
      );

      if (hasContent) {
        // Marca o cabeçalho do artigo
        const heading = container.querySelector(`.art-heading[data-article-id="${articleId}"]`);
        if (heading) {
          heading.setAttribute('data-has-annotation', 'true');
        }

        // Marca o botão de ações do artigo
        const button = container.querySelector(`.art-head-btn[data-article-id="${articleId}"]`);
        if (button) {
          button.setAttribute('data-has-annotation', 'true');
        }

        // Adiciona indicador visual no final do texto do artigo
        const articleElement = container.querySelector(`#${articleId}`);
        if (articleElement) {
          // Verifica se já não existe um indicador
          const existingIndicator = articleElement.querySelector('.annotation-indicator');
          if (!existingIndicator) {
            const indicator = document.createElement('span');
            indicator.className = 'annotation-indicator';
            indicator.innerHTML = ' <span class="annotation-badge" data-article-id="' + articleId + '">📝</span>';
            indicator.title = 'Este artigo possui anotações - clique para visualizar';

            // Adiciona o indicador no final do conteúdo do artigo
            articleElement.appendChild(indicator);
          }
        }
      }
    });
  }, [containerRef, annotations, dependency]);

  return null;
}
