import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../firebase';
import type { ArticleAnnotation, AnnotationFormData } from '../types/annotations';

const COLLECTION_NAME = 'annotations';

export class AnnotationService {
  /**
   * Busca uma anotação específica para um artigo
   */
  static async getAnnotation(
    leiId: string, 
    articleId: string, 
    userId: string
  ): Promise<ArticleAnnotation | null> {
    if (!db) {
      console.warn('Firebase não configurado');
      return null;
    }

    try {
      const annotationId = `${leiId}_${articleId}_${userId}`;
      const docRef = doc(db, COLLECTION_NAME, annotationId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data();
        return {
          id: docSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as ArticleAnnotation;
      }

      return null;
    } catch (error) {
      console.error('Erro ao buscar anotação:', error);
      return null;
    }
  }

  /**
   * Salva ou atualiza uma anotação
   */
  static async saveAnnotation(
    leiId: string,
    articleId: string,
    userId: string,
    data: AnnotationFormData
  ): Promise<void> {
    if (!db) {
      console.warn('Firebase não configurado');
      return;
    }

    try {
      const annotationId = `${leiId}_${articleId}_${userId}`;
      const docRef = doc(db, COLLECTION_NAME, annotationId);
      
      // Verifica se já existe
      const existingDoc = await getDoc(docRef);
      
      if (existingDoc.exists()) {
        // Atualiza documento existente
        await updateDoc(docRef, {
          ...data,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Cria novo documento
        await setDoc(docRef, {
          id: annotationId,
          articleId,
          leiId,
          userId,
          ...data,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
      }
    } catch (error) {
      console.error('Erro ao salvar anotação:', error);
      throw new Error('Falha ao salvar anotação');
    }
  }

  /**
   * Busca todas as anotações de uma lei para um usuário
   */
  static async getAnnotationsForLaw(
    leiId: string, 
    userId: string
  ): Promise<ArticleAnnotation[]> {
    if (!db) {
      console.warn('Firebase não configurado');
      return [];
    }

    try {
      const q = query(
        collection(db, COLLECTION_NAME),
        where('leiId', '==', leiId),
        where('userId', '==', userId)
      );

      const querySnapshot = await getDocs(q);
      const annotations: ArticleAnnotation[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        annotations.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as ArticleAnnotation);
      });

      return annotations;
    } catch (error) {
      console.error('Erro ao buscar anotações da lei:', error);
      return [];
    }
  }

  /**
   * Remove uma anotação
   */
  static async deleteAnnotation(
    leiId: string,
    articleId: string,
    userId: string
  ): Promise<void> {
    if (!db) {
      console.warn('Firebase não configurado');
      return;
    }

    try {
      const annotationId = `${leiId}_${articleId}_${userId}`;
      const docRef = doc(db, COLLECTION_NAME, annotationId);
      await updateDoc(docRef, {
        explicacao: '',
        doutrina: '',
        jurisprudencia: '',
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Erro ao remover anotação:', error);
      throw new Error('Falha ao remover anotação');
    }
  }
}
