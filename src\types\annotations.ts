export interface ArticleAnnotation {
  id: string; // ID único da anotação
  articleId: string; // ID do artigo (ex: "art-1")
  leiId: string; // ID da lei
  userId: string; // ID do usuário
  explicacao?: string; // Explicação rápida com caso prático
  doutrina?: string; // Doutrina
  jurisprudencia?: string; // Jurisprudência
  createdAt: Date;
  updatedAt: Date;
}

export interface AnnotationFormData {
  explicacao: string;
  doutrina: string;
  jurisprudencia: string;
}

export interface AIGenerationOptions {
  explicacao: boolean;
  doutrina: boolean;
  jurisprudencia: boolean;
}

export interface AnnotationModalProps {
  isOpen: boolean;
  onClose: () => void;
  articleId: string;
  articleText: string;
  leiId: string;
  annotation?: ArticleAnnotation;
  onSave: (data: AnnotationFormData) => Promise<void>;
  onGenerateAI: (options: AIGenerationOptions) => Promise<Partial<AnnotationFormData>>;
}

export interface AIConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (options: AIGenerationOptions) => void;
  hasExistingContent: {
    explicacao: boolean;
    doutrina: boolean;
    jurisprudencia: boolean;
  };
}
