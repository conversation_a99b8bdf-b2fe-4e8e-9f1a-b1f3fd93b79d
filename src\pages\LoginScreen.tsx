import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { STRIPE_BACKEND_URL, getStripePublishableKey } from "../utils/env";
import { fetchJson } from "../utils/http";
import {
  GoogleAuthProvider,
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
} from "firebase/auth";
import { auth } from "../firebase";
import { getFunctions, httpsCallable } from "firebase/functions";
import { ThemeToggle } from "../components/ui/ThemeToggle";
import Modal from "../components/ui/Modal";
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe with environment variable
let stripePromise: Promise<any> | null = null;

function getStripePromise() {
  if (!stripePromise) {
    try {
      const publishableKey = getStripePublishableKey();
      stripePromise = loadStripe(publishableKey);
    } catch (error) {
      console.error('Failed to initialize Stripe:', error);
      toast.error('Stripe não está configurado corretamente');
      return null;
    }
  }
  return stripePromise;
}

interface LoginProps {
  minimal?: boolean;
  onGuestContinue?: () => void;
  onLoginSuccess?: () => void;
}

export function LoginScreen({ minimal = false, onGuestContinue, onLoginSuccess }: LoginProps) {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);

  // State for modal flow
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [selectedPriceId, setSelectedPriceId] = useState<string | null>(null);

  useEffect(() => {
    // Only redirect if on the main login page, not in a modal
    if (auth?.currentUser && !minimal) {
      navigate("/home", { replace: true });
    }
  }, [minimal, navigate]);

  const handleAuthSuccess = () => {
    localStorage.removeItem('guest_mode');
    localStorage.removeItem('ia_credits_left');
    if (onLoginSuccess) {
      onLoginSuccess();
    } else {
      navigate("/home", { replace: true });
    }
  };

  async function handleGoogle() {
    if (!auth) return toast.error("Firebase não configurado");
    try {
      setLoading(true);
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
      toast.success("Login realizado!");
      handleAuthSuccess();
    } catch (err: any) {
      toast.error(err.message || 'Falha no login');
    } finally {
      setLoading(false);
    }
  }

  async function handleEmailPass(e: React.FormEvent) {
    e.preventDefault();
    if (!auth) return toast.error("Firebase não configurado");
    try {
      setLoading(true);
      if (isRegistering) {
        await createUserWithEmailAndPassword(auth, email, password);
        toast.success("Conta criada!");
      } else {
        await signInWithEmailAndPassword(auth, email, password);
        toast.success("Login realizado!");
      }
      handleAuthSuccess();
    } catch (err: any) {
      const code = err.code as string | undefined;
      if (code === 'auth/admin-restricted-operation') {
        toast.error('Cadastro desativado. Contate o administrador.');
      } else {
        toast.error(err.message || 'Falha no login');
      }
    } finally {
      setLoading(false);
    }
  }

  async function handleGuest() {
    localStorage.setItem("guest_mode", "1");
    if (!localStorage.getItem('ia_credits_left') || parseInt(localStorage.getItem('ia_credits_left')||'0',10)<=0) {
      localStorage.setItem('ia_credits_left', '5');
    }
    // Clean up old guest highlights (both old 'anon' and new 'guest' keys)
    Object.keys(localStorage).forEach((k) => {
      if (k.startsWith('grifos-anon-') || k.startsWith('grifos-guest-')) {
        localStorage.removeItem(k);
      }
    });
    if (auth) {
      try { await signOut(auth); } catch {}
    }
    if (!minimal) {
      navigate("/home", { replace: true });
    } else {
      onGuestContinue?.();
    }
  }

  async function startCheckout(priceId: string) {
    setLoading(true);
    try {
      const backendUrl = `${STRIPE_BACKEND_URL}/create-checkout-session`;

      const successUrl = `${window.location.origin}/home`;
      const cancelUrl = window.location.origin;

      // Chama o backend local via fetch
      const session = await fetchJson<{ id: string }>(backendUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ priceId, successUrl, cancelUrl }),
        timeout: 10000,
      });
      const sessionId = session.id;

      // Redireciona para o checkout do Stripe.
      const stripePromiseInstance = getStripePromise();
      if (!stripePromiseInstance) {
        toast.error("Stripe não está configurado corretamente.");
        return;
      }

      const stripe = await stripePromiseInstance;
      if (stripe) {
        await stripe.redirectToCheckout({ sessionId });
      } else {
        toast.error("Não foi possível carregar a página de pagamento.");
      }
    } catch (error: any) {
      console.error("Erro ao criar sessão de checkout:", error);
      const msg =
        error?.message === 'Failed to fetch'
          ? 'Não foi possível conectar ao servidor de pagamentos.'
          : error.message || 'Falha ao iniciar o processo de assinatura.';
      toast.error(msg);
    } finally {
      setLoading(false);
    }
  }

  async function handleSubscription(priceId: string) {
    if (!auth) return toast.error("Serviço de autenticação indisponível.");
    if (auth.currentUser) {
      await startCheckout(priceId);
    } else {
      setSelectedPriceId(priceId);
      setIsLoginModalOpen(true);
    }
  }

  const containerClass = minimal
    ? "flex flex-col items-center justify-center p-4 max-h-[95vh] overflow-auto"
    : "min-h-screen flex flex-col items-center justify-center p-6 bg-gradient-to-br from-neutral-50 via-white to-neutral-100 dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-950";

  // The content of the login form, to be reused in the page and the modal
  const loginFormContent = (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white dark:bg-neutral-900 rounded-3xl shadow-xl border border-neutral-200 dark:border-neutral-800 p-8 animate-scaleIn">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-white mb-2">
            {isRegistering ? "Criar Conta" : "Bem-vindo"}
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400 text-sm">
            {isRegistering ? "Crie sua conta para começar" : "Entre na sua conta"}
          </p>
        </div>

        {/* Google Button */}
        <button
          onClick={handleGoogle}
          disabled={loading}
          className="w-full py-3 px-4 mb-6 rounded-xl bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 text-neutral-700 dark:text-neutral-300 shadow-sm hover:shadow-md flex items-center justify-center gap-3 transition-all duration-200 font-medium hover-lift"
        >
          <img src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg" alt="Google" className="w-5 h-5" />
          Continuar com Google
        </button>

        {/* Divider */}
        <div className="relative mb-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-neutral-200 dark:border-neutral-700"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-3 bg-white dark:bg-neutral-900 text-neutral-500">ou</span>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleEmailPass} className="space-y-4 mb-6">
          <input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="input-modern"
            required
          />
          <input
            type="password"
            placeholder="Senha"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="input-modern"
            required
          />
          <button
            type="submit"
            disabled={loading}
            className="btn-primary w-full"
          >
            {isRegistering ? "Criar Conta" : "Entrar"}
          </button>
        </form>

        {/* Toggle */}
        <button
          onClick={() => setIsRegistering(!isRegistering)}
          className="w-full text-sm text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white transition-colors mb-4"
        >
          {isRegistering ? "Já tem conta? Entrar" : "Criar nova conta"}
        </button>

        {/* Guest Mode */}
        <div className="pt-4 border-t border-neutral-200 dark:border-neutral-700 text-center">
          <button
            onClick={handleGuest}
            className="text-sm text-neutral-500 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors"
          >
            Continuar sem conta
          </button>
        </div>
      </div>
    </div>
  );

  if (minimal) {
    return (
      <div className="max-h-[85vh] overflow-y-auto">
        {loginFormContent}

        {/* Compact Plans Section for Modal */}
        <div className="mt-8 pt-6 border-t border-neutral-200 dark:border-neutral-700">
          <h3 className="text-lg font-bold text-center text-neutral-900 dark:text-white mb-6">Nossos Planos</h3>

          <div className="space-y-3">
            {/* Plan 1 - Compact */}
            <div className="bg-neutral-50 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-2xl p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-bold text-neutral-900 dark:text-white">Essencial</h4>
                  <p className="text-lg font-bold text-neutral-900 dark:text-white">
                    R$9<span className="text-sm font-normal text-neutral-600 dark:text-neutral-400">/mês</span>
                  </p>
                </div>
                <button
                  onClick={() => handleSubscription('price_1Ri1PbGaOqxUlEi69DL9vmnl')}
                  className="btn-outline px-4 py-2 text-sm"
                >
                  Escolher
                </button>
              </div>
              <ul className="text-xs space-y-1 text-neutral-600 dark:text-neutral-400">
                <li>• Grifos na nuvem</li>
                <li>• App mobile</li>
                <li>• API personalizada</li>
              </ul>
            </div>

            {/* Plan 2 - Compact */}
            <div className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950 dark:to-accent-950 border border-primary-200 dark:border-primary-800 rounded-2xl p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <div className="flex items-center gap-2">
                    <h4 className="font-bold text-neutral-900 dark:text-white">Premium</h4>
                    <span className="bg-primary-500 text-white px-2 py-0.5 rounded-full text-xs">Popular</span>
                  </div>
                  <p className="text-lg font-bold text-neutral-900 dark:text-white">
                    R$19<span className="text-sm font-normal text-neutral-600 dark:text-neutral-400">/mês</span>
                  </p>
                </div>
                <button
                  onClick={() => handleSubscription('price_1PjL8PGaOqxUlEi65sM2pYtP')}
                  className="btn-primary px-4 py-2 text-sm"
                >
                  Escolher
                </button>
              </div>
              <ul className="text-xs space-y-1 text-neutral-600 dark:text-neutral-400">
                <li>• Tudo do Essencial</li>
                <li>• IA ilimitada</li>
                <li>• Suporte prioritário</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={containerClass}>
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      
      {loginFormContent}

      {/* Seção de Planos de Assinatura */}
      <div className="mt-16 w-full max-w-5xl">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-neutral-900 dark:text-white mb-4">Escolha seu plano</h3>
          <p className="text-neutral-600 dark:text-neutral-400 text-lg">Acesso ilimitado a todas as funcionalidades</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Plan 1 */}
          <div className="bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift animate-fadeUp">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-2xl mb-4">
                <svg className="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <h4 className="text-xl font-bold text-neutral-900 dark:text-white mb-2">Essencial</h4>
              <div className="text-4xl font-bold text-neutral-900 dark:text-white">
                R$9<span className="text-lg font-normal text-neutral-600 dark:text-neutral-400">/mês</span>
              </div>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-center gap-3">
                <div className="w-5 h-5 bg-accent-100 dark:bg-accent-900 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-accent-600 dark:text-accent-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-neutral-700 dark:text-neutral-300">Grifos salvos na nuvem</span>
              </li>
              <li className="flex items-center gap-3">
                <div className="w-5 h-5 bg-accent-100 dark:bg-accent-900 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-accent-600 dark:text-accent-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-neutral-700 dark:text-neutral-300">App mobile com sincronização</span>
              </li>
              <li className="flex items-center gap-3">
                <div className="w-5 h-5 bg-accent-100 dark:bg-accent-900 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-accent-600 dark:text-accent-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-neutral-700 dark:text-neutral-300">API Key personalizada</span>
              </li>
            </ul>

            <button
              onClick={() => handleSubscription('price_1Ri1PbGaOqxUlEi69DL9vmnl')}
              className="btn-outline w-full"
            >
              Começar agora
            </button>
          </div>

          {/* Plan 2 - Featured */}
          <div className="bg-gradient-to-br from-primary-500 to-accent-500 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover-lift animate-fadeUp animate-delay-100 relative">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span className="bg-white text-primary-600 px-4 py-1 rounded-full text-sm font-medium shadow-lg">
                Mais Popular
              </span>
            </div>

            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-2xl mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h4 className="text-xl font-bold text-white mb-2">Premium</h4>
              <div className="text-4xl font-bold text-white">
                R$19<span className="text-lg font-normal text-white/80">/mês</span>
              </div>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-center gap-3">
                <div className="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-white">Tudo do plano Essencial</span>
              </li>
              <li className="flex items-center gap-3">
                <div className="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-white">IA ilimitada (GPT, Gemini, Claude)</span>
              </li>
              <li className="flex items-center gap-3">
                <div className="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-white">Suporte prioritário</span>
              </li>
            </ul>

            <button
              onClick={() => handleSubscription('price_1PjL8PGaOqxUlEi65sM2pYtP')}
              className="w-full py-3 px-6 bg-white text-primary-600 rounded-xl font-medium hover:bg-neutral-50 transition-all duration-200 hover:shadow-lg"
            >
              Começar agora
            </button>
          </div>
        </div>
      </div>

      <Modal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)}>
        <LoginScreen 
          minimal
          onLoginSuccess={() => {
            setIsLoginModalOpen(false);
            if (selectedPriceId) {
              startCheckout(selectedPriceId);
            }
          }}
          onGuestContinue={() => setIsLoginModalOpen(false)}
        />
      </Modal>
    </div>
  );
} 