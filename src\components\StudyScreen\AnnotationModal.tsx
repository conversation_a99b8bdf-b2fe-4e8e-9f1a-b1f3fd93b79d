import React, { useState, useEffect } from 'react';
import { Bot, Save, X, FileText } from 'lucide-react';
import toast from 'react-hot-toast';
import type { AnnotationModalProps, AnnotationFormData, AIGenerationOptions } from '../../types/annotations';
import { AIConfirmationModal } from './AIConfirmationModal';

export const AnnotationModal: React.FC<AnnotationModalProps> = ({
  isOpen,
  onClose,
  articleId,
  articleText,
  leiId,
  annotation,
  onSave,
  onGenerateAI,
}) => {
  const [formData, setFormData] = useState<AnnotationFormData>({
    explicacao: '',
    doutrina: '',
    jurisprudencia: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [showAIConfirmation, setShowAIConfirmation] = useState(false);

  // Carrega dados da anotação existente
  useEffect(() => {
    if (annotation) {
      setFormData({
        explicacao: annotation.explicacao || '',
        doutrina: annotation.doutrina || '',
        jurisprudencia: annotation.jurisprudencia || '',
      });
    } else {
      setFormData({
        explicacao: '',
        doutrina: '',
        jurisprudencia: '',
      });
    }
  }, [annotation]);

  if (!isOpen) return null;

  const handleInputChange = (field: keyof AnnotationFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      await onSave(formData);
      toast.success('Anotação salva com sucesso!');
      onClose();
    } catch (error) {
      console.error('Erro ao salvar anotação:', error);
      toast.error('Erro ao salvar anotação');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAIGeneration = async (options: AIGenerationOptions) => {
    try {
      setIsLoading(true);
      const generatedContent = await onGenerateAI(options);
      
      setFormData(prev => ({
        ...prev,
        ...(options.explicacao && generatedContent.explicacao ? { explicacao: generatedContent.explicacao } : {}),
        ...(options.doutrina && generatedContent.doutrina ? { doutrina: generatedContent.doutrina } : {}),
        ...(options.jurisprudencia && generatedContent.jurisprudencia ? { jurisprudencia: generatedContent.jurisprudencia } : {}),
      }));

      toast.success('Conteúdo gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar conteúdo:', error);
      toast.error('Erro ao gerar conteúdo com IA');
    } finally {
      setIsLoading(false);
    }
  };

  const hasExistingContent = {
    explicacao: !!formData.explicacao.trim(),
    doutrina: !!formData.doutrina.trim(),
    jurisprudencia: !!formData.jurisprudencia.trim(),
  };

  const articleTitle = `Art. ${articleId.replace('art-', '')}`;

  return (
    <>
      <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center" onClick={onClose}>
        <div
          className="modal-bg p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <FileText className="text-primary" size={24} />
              <h3 className="text-lg font-bold">Anotações - {articleTitle}</h3>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowAIConfirmation(true)}
                disabled={isLoading}
                className="flex items-center gap-2 px-3 py-2 text-sm bg-primary/20 hover:bg-primary/30 rounded-lg transition-colors disabled:opacity-50"
              >
                <Bot size={16} />
                Gerar com IA
              </button>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-500/20 rounded-lg transition-colors"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          {/* Article Preview */}
          <div className="mb-4 p-3 bg-gray-500/10 rounded-lg border border-gray-500/20">
            <p className="text-sm opacity-80 line-clamp-3">
              {articleText}
            </p>
          </div>

          {/* Form Fields */}
          <div className="flex-1 overflow-y-auto space-y-4">
            {/* Explicação com Caso Prático */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Explicação Rápida com Caso Prático
              </label>
              <textarea
                value={formData.explicacao}
                onChange={(e) => handleInputChange('explicacao', e.target.value)}
                placeholder="Digite uma explicação clara do artigo com um exemplo prático..."
                className="w-full h-32 p-3 bg-gray-500/10 border border-gray-500/20 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                disabled={isLoading}
              />
            </div>

            {/* Doutrina */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Doutrina
              </label>
              <textarea
                value={formData.doutrina}
                onChange={(e) => handleInputChange('doutrina', e.target.value)}
                placeholder="Digite comentários doutrinários, citações de autores e obras relevantes..."
                className="w-full h-32 p-3 bg-gray-500/10 border border-gray-500/20 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                disabled={isLoading}
              />
            </div>

            {/* Jurisprudência */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Jurisprudência
              </label>
              <textarea
                value={formData.jurisprudencia}
                onChange={(e) => handleInputChange('jurisprudencia', e.target.value)}
                placeholder="Digite precedentes jurisprudenciais relevantes..."
                className="w-full h-32 p-3 bg-gray-500/10 border border-gray-500/20 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-500/20">
            <button
              onClick={onClose}
              disabled={isLoading}
              className="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50/10 transition-colors disabled:opacity-50"
            >
              Cancelar
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 text-sm bg-primary text-white rounded-lg hover:bg-primary/90 disabled:opacity-50 transition-colors"
            >
              <Save size={16} />
              {isLoading ? 'Salvando...' : 'Salvar'}
            </button>
          </div>
        </div>
      </div>

      {/* AI Confirmation Modal */}
      <AIConfirmationModal
        isOpen={showAIConfirmation}
        onClose={() => setShowAIConfirmation(false)}
        onConfirm={handleAIGeneration}
        hasExistingContent={hasExistingContent}
      />
    </>
  );
};
