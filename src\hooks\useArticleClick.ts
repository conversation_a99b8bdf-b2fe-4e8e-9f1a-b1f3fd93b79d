import { useEffect, useCallback, RefObject } from 'react';

interface UseArticleClickProps {
  containerRef: RefObject<HTMLElement>;
  onArticleClick: (articleId: string, articleText: string) => void;
  dependency?: any;
}

export function useArticleClick({ 
  containerRef, 
  onArticleClick, 
  dependency 
}: UseArticleClickProps) {
  
  const handleClick = useCallback((event: Event) => {
    const target = event.target as HTMLElement;

    // Verifica se clicou no botão de ações do artigo ou no cabeçalho do artigo
    // Também verifica elementos pai (delegação de eventos)
    let currentElement = target;
    let isArticleButton = false;
    let isArticleHeading = false;
    let articleId = '';

    // Procura até 3 níveis acima na árvore DOM
    for (let i = 0; i < 3 && currentElement; i++) {
      isArticleButton = currentElement.classList.contains('art-head-btn');
      isArticleHeading = currentElement.classList.contains('art-heading');
      const isAnnotationBadge = currentElement.classList.contains('annotation-badge');
      articleId = currentElement.dataset.articleId || '';

      if ((isArticleButton || isArticleHeading || isAnnotationBadge) && articleId) break;

      currentElement = currentElement.parentElement as HTMLElement;
    }

    const isAnnotationBadge = target.classList.contains('annotation-badge') ||
                          target.closest('.annotation-badge') !== null;

    if (!isArticleButton && !isArticleHeading && !isAnnotationBadge) return;
    if (!articleId) return;

    event.preventDefault();
    event.stopPropagation();
    
    // Busca o elemento do artigo completo
    const container = containerRef.current;
    if (!container) return;

    const articleElement = container.querySelector(`#${articleId}`);
    if (!articleElement) return;

    // Extrai o texto completo do artigo (incluindo parágrafos e incisos)
    let articleText = articleElement.textContent || '';

    // Inclui elementos relacionados (parágrafos, incisos) que vêm após o artigo
    let sibling = articleElement.nextElementSibling;
    while (sibling && !sibling.classList.contains('art')) {
      if (sibling.classList.contains('para') ||
          sibling.classList.contains('inciso') ||
          sibling.classList.contains('alinea')) {
        articleText += `\n${sibling.textContent || ''}`;
      }
      sibling = sibling.nextElementSibling;
    }

    onArticleClick(articleId, articleText.trim());
  }, [containerRef, onArticleClick]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Adiciona event listeners para cliques nos artigos
    container.addEventListener('click', handleClick);

    return () => {
      container.removeEventListener('click', handleClick);
    };
  }, [handleClick, dependency]);

  return null;
}
