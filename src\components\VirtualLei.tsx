import React, { useCallback, useImperativeHandle, useLayoutEffect, useRef } from "react";
import { VariableSizeList as List, ListChildComponentProps } from "react-window";
import AutoSizer from "react-virtualized-auto-sizer";
import { Article } from "./Article";

export interface VirtualLeiHandle {
  scrollTo(index: number): void;
}

interface Props {
  artigos: string[];
  onHighlightSelect?: (sel: Selection, articleId: string) => void;
  onAnnotationClick?: (articleId: string, articleText: string) => void;
  removeHighlight: (id: string) => Promise<void>;
  estAltura?: number; // fallback size
}

/** Virtualiza uma lista de artigos usando react-window (VariableSizeList).
 * Mantém no DOM apenas os itens visíveis + overscan, garantindo que o scroll
 * nativo funcione sem recalibração da barra.
 */
export const VirtualLei = React.forwardRef<VirtualLeiHandle, Props>(
({ artigos, onHighlightSelect, onAnnotationClick, removeHighlight, estAltura = 200 }, ref) => {
  const heightsRef = useRef<number[]>([]);
  const listRef = useRef<List>(null);

  useImperativeHandle(ref, () => ({
    scrollTo: (index: number) => {
      // Primeiro scroll imediato
      listRef.current?.scrollToItem(index, 'start');
      // Segundo scroll no próximo frame para ajustar após cálculo das alturas
      requestAnimationFrame(() => {
        listRef.current?.scrollToItem(index, 'start');
      });
      // Terceiro scroll após pequeno delay para mobile (linhas longas geram alturas maiores)
      setTimeout(() => {
        listRef.current?.scrollToItem(index, 'start');
      }, 120);
    },
  }));

  const getSize = useCallback((index: number) => {
    return heightsRef.current[index] || estAltura;
  }, [estAltura]);

  // Row renderer
  const Row = ({ index, style }: ListChildComponentProps) => {
    const artId = `art${index + 1}`;

    const rowRef = useRef<HTMLDivElement | null>(null);

    // mede altura real e avisa a lista se mudou
    useLayoutEffect(() => {
      const el = rowRef.current;
      if (!el) return;
      const h = el.getBoundingClientRect().height;
      if (h && h !== heightsRef.current[index]) {
        heightsRef.current[index] = h;
        listRef.current?.resetAfterIndex(index);
      }
    });

    return (
      <div ref={rowRef} style={style}>
        <Article
          id={artId}
          text={artigos[index]}
          {...(onHighlightSelect && { onHighlightSelect: onHighlightSelect })}
          onAnnotationClick={onAnnotationClick}
          removeHighlight={removeHighlight}
        />
      </div>
    );
  };

  return (
    <div style={{ flex: 1, height: "100%" }}>
      <AutoSizer>
        {({ height, width }) => (
          <List
            height={height}
            width={width}
            itemCount={artigos.length}
            itemSize={getSize}
            ref={listRef}
            overscanCount={10}
            itemKey={(index) => `art-${index}`}
          >
            {Row}
          </List>
        )}
      </AutoSizer>
    </div>
  );
});
