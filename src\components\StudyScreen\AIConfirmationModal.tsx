import React, { useState } from 'react';
import { Bo<PERSON>, AlertTriangle } from 'lucide-react';
import type { AIConfirmationModalProps, AIGenerationOptions } from '../../types/annotations';

export const AIConfirmationModal: React.FC<AIConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  hasExistingContent,
}) => {
  const [selectedOptions, setSelectedOptions] = useState<AIGenerationOptions>({
    explicacao: true,
    doutrina: true,
    jurisprudencia: true,
  });

  if (!isOpen) return null;

  const handleOptionChange = (field: keyof AIGenerationOptions) => {
    setSelectedOptions(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleConfirm = () => {
    onConfirm(selectedOptions);
    onClose();
  };

  const hasAnyExistingContent = Object.values(hasExistingContent).some(<PERSON>ole<PERSON>);

  return (
    <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center" onClick={onClose}>
      <div
        className="modal-bg p-6 rounded-lg shadow-xl max-w-md w-full mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center gap-3 mb-4">
          <Bot className="text-primary" size={24} />
          <h3 className="text-lg font-bold">Gerar Conteúdo com IA</h3>
        </div>

        {hasAnyExistingContent && (
          <div className="flex items-start gap-2 mb-4 p-3 bg-amber-500/10 border border-amber-500/20 rounded-lg">
            <AlertTriangle className="text-amber-500 mt-0.5" size={16} />
            <div className="text-sm">
              <p className="font-medium text-amber-500 mb-1">Atenção!</p>
              <p className="text-amber-200">
                Você já possui conteúdo em alguns campos. O conteúdo gerado pela IA substituirá o existente.
              </p>
            </div>
          </div>
        )}

        <div className="space-y-3 mb-6">
          <p className="text-sm opacity-80 mb-3">
            Selecione quais campos deseja gerar automaticamente:
          </p>

          <label className="flex items-center gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={selectedOptions.explicacao}
              onChange={() => handleOptionChange('explicacao')}
              className="w-4 h-4 text-primary bg-transparent border-2 border-gray-300 rounded focus:ring-primary focus:ring-2"
            />
            <div className="flex-1">
              <span className="font-medium">Explicação com Caso Prático</span>
              {hasExistingContent.explicacao && (
                <span className="text-xs text-amber-400 ml-2">(substituirá conteúdo existente)</span>
              )}
            </div>
          </label>

          <label className="flex items-center gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={selectedOptions.doutrina}
              onChange={() => handleOptionChange('doutrina')}
              className="w-4 h-4 text-primary bg-transparent border-2 border-gray-300 rounded focus:ring-primary focus:ring-2"
            />
            <div className="flex-1">
              <span className="font-medium">Doutrina</span>
              {hasExistingContent.doutrina && (
                <span className="text-xs text-amber-400 ml-2">(substituirá conteúdo existente)</span>
              )}
            </div>
          </label>

          <label className="flex items-center gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={selectedOptions.jurisprudencia}
              onChange={() => handleOptionChange('jurisprudencia')}
              className="w-4 h-4 text-primary bg-transparent border-2 border-gray-300 rounded focus:ring-primary focus:ring-2"
            />
            <div className="flex-1">
              <span className="font-medium">Jurisprudência</span>
              {hasExistingContent.jurisprudencia && (
                <span className="text-xs text-amber-400 ml-2">(substituirá conteúdo existente)</span>
              )}
            </div>
          </label>
        </div>

        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50/10 transition-colors"
          >
            Cancelar
          </button>
          <button
            onClick={handleConfirm}
            disabled={!Object.values(selectedOptions).some(Boolean)}
            className="flex-1 px-4 py-2 text-sm bg-primary text-white rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Gerar Conteúdo
          </button>
        </div>
      </div>
    </div>
  );
};
