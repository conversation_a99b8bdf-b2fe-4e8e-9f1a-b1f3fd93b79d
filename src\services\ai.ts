import { getOpenA<PERSON><PERSON><PERSON>, getG<PERSON>ini<PERSON>ey } from '../utils/env';

async function callOpenAI(messages: any[]): Promise<string> {
  const apiKey = getOpenAIKey();
  if (!api<PERSON>ey) throw new Error('OpenAI API key não configurada');
  const res = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${apiKey}`,
    },
    body: JSON.stringify({
      model: 'gpt-3.5-turbo',
      messages,
      temperature: 0.4,
      max_tokens: 400,
    }),
  });
  if (!res.ok) throw new Error('Falha ao contactar OpenAI');
  const data = await res.json();
  return data.choices?.[0]?.message?.content ?? '';
}

async function callGemini(prompt: string, cfg = { temperature: 0.4, maxOutputTokens: 400 }): Promise<string> {
  const apiKey = getGeminiKey();
  if (!api<PERSON><PERSON>) throw new Error('Gemini API key não configurada');
  const model = import.meta.env.VITE_GEMINI_MODEL || 'gemini-2.0-flash';
  const ver = import.meta.env.VITE_GEMINI_API_VERSION || 'v1';
  const res = await fetch(`https://generativelanguage.googleapis.com/${ver}/models/${model}:generateContent?key=${apiKey}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: cfg,
    }),
  });
  if (!res.ok) throw new Error('Falha ao contactar Gemini');
  const data = await res.json();
  return data.candidates?.[0]?.content?.parts?.[0]?.text ?? '';
}

export async function explainLawBrief(text: string): Promise<string> {
  const prompt = 'Você é um jurista renomado. Explique o dispositivo legal exibido em linguagem simples.';
  try {
    return await callOpenAI([
      { role: 'system', content: prompt },
      { role: 'user', content: text },
    ]);
  } catch (openAIError) {
    console.warn('OpenAI failed, trying Gemini:', openAIError);
    try {
      return await callGemini(`${prompt}\n${text}`);
    } catch (geminiError) {
      console.error('Both AI services failed:', { openAIError, geminiError });
      throw new Error('Serviços de IA indisponíveis no momento. Tente novamente mais tarde.');
    }
  }
}

export async function explainLawWithExample(text: string): Promise<string> {
  const prompt =
    'Você é um jurista renomado. Explique o dispositivo legal em linguagem simples e forneça um exemplo prático de aplicação.';
  try {
    return await callOpenAI([
      { role: 'system', content: prompt },
      { role: 'user', content: text },
    ]);
  } catch (openAIError) {
    console.warn('OpenAI failed, trying Gemini:', openAIError);
    try {
      return await callGemini(`${prompt}\n${text}`);
    } catch (geminiError) {
      console.error('Both AI services failed:', { openAIError, geminiError });
      throw new Error('Serviços de IA indisponíveis no momento. Tente novamente mais tarde.');
    }
  }
}

export async function fetchJurisprudence(text: string): Promise<string> {
  const prompt = 'Liste, de forma objetiva, 3 precedentes relevantes de tribunais superiores brasileiros que aplicam o dispositivo legal exibido. Indique corte, número do processo, ano e um breve resumo do entendimento.';
  try {
    return await callOpenAI([
      { role: 'system', content: prompt },
      { role: 'user', content: text },
    ]);
  } catch (openAIError) {
    console.warn('OpenAI failed, trying Gemini:', openAIError);
    try {
      return await callGemini(`${prompt}\n${text}`, { temperature: 0.3, maxOutputTokens: 400 });
    } catch (geminiError) {
      console.error('Both AI services failed:', { openAIError, geminiError });
      throw new Error('Serviços de IA indisponíveis no momento. Tente novamente mais tarde.');
    }
  }
}

export async function generateExplicacaoComCaso(text: string): Promise<string> {
  const prompt = 'Você é um jurista renomado. Forneça uma explicação rápida e clara do dispositivo legal apresentado, incluindo um caso prático de aplicação. Seja conciso e didático.';
  try {
    return await callOpenAI([
      { role: 'system', content: prompt },
      { role: 'user', content: text },
    ]);
  } catch (openAIError) {
    console.warn('OpenAI failed, trying Gemini:', openAIError);
    try {
      return await callGemini(`${prompt}\n${text}`);
    } catch (geminiError) {
      console.error('Both AI services failed:', { openAIError, geminiError });
      throw new Error('Serviços de IA indisponíveis no momento. Tente novamente mais tarde.');
    }
  }
}

export async function generateDoutrina(text: string): Promise<string> {
  const prompt = 'Você é um jurista renomado. Forneça comentários doutrinários relevantes sobre o dispositivo legal apresentado, citando autores e obras importantes quando possível.';
  try {
    return await callOpenAI([
      { role: 'system', content: prompt },
      { role: 'user', content: text },
    ]);
  } catch (openAIError) {
    console.warn('OpenAI failed, trying Gemini:', openAIError);
    try {
      return await callGemini(`${prompt}\n${text}`);
    } catch (geminiError) {
      console.error('Both AI services failed:', { openAIError, geminiError });
      throw new Error('Serviços de IA indisponíveis no momento. Tente novamente mais tarde.');
    }
  }
}
