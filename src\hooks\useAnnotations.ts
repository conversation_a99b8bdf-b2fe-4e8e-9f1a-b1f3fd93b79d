import { useState, useEffect, useCallback } from 'react';
import { auth } from '../firebase';
import { AnnotationService } from '../services/annotations';
import { generateExplicacaoComCaso, generateDoutrina, fetchJurisprudence } from '../services/ai';
import type { 
  ArticleAnnotation, 
  AnnotationFormData, 
  AIGenerationOptions 
} from '../types/annotations';

export function useAnnotations(leiId: string) {
  const [annotations, setAnnotations] = useState<Map<string, ArticleAnnotation>>(new Map());
  const [isLoading, setIsLoading] = useState(false);

  const userId = auth?.currentUser?.uid || 'anonymous';

  // Carrega todas as anotações da lei
  const loadAnnotations = useCallback(async () => {
    if (!leiId) return;

    try {
      setIsLoading(true);
      const annotationsList = await AnnotationService.getAnnotationsForLaw(leiId, userId);
      
      const annotationsMap = new Map<string, ArticleAnnotation>();
      annotationsList.forEach(annotation => {
        annotationsMap.set(annotation.articleId, annotation);
      });
      
      setAnnotations(annotationsMap);
    } catch (error) {
      console.error('Erro ao carregar anotações:', error);
    } finally {
      setIsLoading(false);
    }
  }, [leiId, userId]);

  // Carrega anotações quando a lei muda
  useEffect(() => {
    loadAnnotations();
  }, [loadAnnotations]);

  // Busca uma anotação específica
  const getAnnotation = useCallback(async (articleId: string): Promise<ArticleAnnotation | null> => {
    // Primeiro verifica no cache local
    const cached = annotations.get(articleId);
    if (cached) return cached;

    // Se não estiver no cache, busca no Firebase
    try {
      const annotation = await AnnotationService.getAnnotation(leiId, articleId, userId);
      if (annotation) {
        setAnnotations(prev => new Map(prev).set(articleId, annotation));
      }
      return annotation;
    } catch (error) {
      console.error('Erro ao buscar anotação:', error);
      return null;
    }
  }, [leiId, userId, annotations]);

  // Salva uma anotação
  const saveAnnotation = useCallback(async (
    articleId: string, 
    data: AnnotationFormData
  ): Promise<void> => {
    try {
      await AnnotationService.saveAnnotation(leiId, articleId, userId, data);
      
      // Atualiza o cache local
      const existingAnnotation = annotations.get(articleId);
      const updatedAnnotation: ArticleAnnotation = {
        id: existingAnnotation?.id || `${leiId}_${articleId}_${userId}`,
        articleId,
        leiId,
        userId,
        ...data,
        createdAt: existingAnnotation?.createdAt || new Date(),
        updatedAt: new Date(),
      };
      
      setAnnotations(prev => new Map(prev).set(articleId, updatedAnnotation));
    } catch (error) {
      console.error('Erro ao salvar anotação:', error);
      throw error;
    }
  }, [leiId, userId, annotations]);

  // Gera conteúdo com IA
  const generateWithAI = useCallback(async (
    articleText: string,
    options: AIGenerationOptions
  ): Promise<Partial<AnnotationFormData>> => {
    const results: Partial<AnnotationFormData> = {};

    try {
      // Gera explicação com caso prático
      if (options.explicacao) {
        try {
          results.explicacao = await generateExplicacaoComCaso(articleText);
        } catch (error) {
          console.error('Erro ao gerar explicação:', error);
        }
      }

      // Gera doutrina
      if (options.doutrina) {
        try {
          results.doutrina = await generateDoutrina(articleText);
        } catch (error) {
          console.error('Erro ao gerar doutrina:', error);
        }
      }

      // Gera jurisprudência
      if (options.jurisprudencia) {
        try {
          results.jurisprudencia = await fetchJurisprudence(articleText);
        } catch (error) {
          console.error('Erro ao gerar jurisprudência:', error);
        }
      }

      return results;
    } catch (error) {
      console.error('Erro na geração com IA:', error);
      throw error;
    }
  }, []);

  // Remove uma anotação
  const deleteAnnotation = useCallback(async (articleId: string): Promise<void> => {
    try {
      await AnnotationService.deleteAnnotation(leiId, articleId, userId);
      
      // Remove do cache local
      setAnnotations(prev => {
        const newMap = new Map(prev);
        newMap.delete(articleId);
        return newMap;
      });
    } catch (error) {
      console.error('Erro ao remover anotação:', error);
      throw error;
    }
  }, [leiId, userId]);

  // Verifica se um artigo tem anotações
  const hasAnnotation = useCallback((articleId: string): boolean => {
    const annotation = annotations.get(articleId);
    if (!annotation) return false;
    
    return !!(
      annotation.explicacao?.trim() ||
      annotation.doutrina?.trim() ||
      annotation.jurisprudencia?.trim()
    );
  }, [annotations]);

  return {
    annotations,
    isLoading,
    getAnnotation,
    saveAnnotation,
    generateWithAI,
    deleteAnnotation,
    hasAnnotation,
    loadAnnotations,
  };
}
