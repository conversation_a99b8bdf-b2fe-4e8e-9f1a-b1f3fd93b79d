import React, { useEffect, useState, useRef, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ChevronLeft } from "lucide-react";
import { ThemeToggle } from "../components/ui/ThemeToggle";
import toast from "react-hot-toast";
import { useDevice } from "../hooks/useDevice";
import { useTextSelection, getNodeFromPath, validateSelection } from "../hooks/useTextSelection";
import { useGuestMode } from '../hooks/useGuestMode';
import { useFontSize } from '../hooks/useFontSize';
import { useRemoteLaw } from '../hooks/useRemoteLaw';
import { useArticlePopup } from '../hooks/useArticlePopup';
import { useAIDialog } from '../hooks/useAIDialog';
import { useHighlights } from "../hooks/useHighlights";
import { useAnnotations } from "../hooks/useAnnotations";
import { useArticleClick } from "../hooks/useArticleClick";
import { useAnnotationIndicators } from "../hooks/useAnnotationIndicators";
import { useChunkedRenderer } from "../hooks/useChunkedRenderer";
import { useHeaderContext } from "../hooks/useHeaderContext";
import { useLawContext } from "../hooks/useLawContext";
import { useTOC } from '../hooks/useTOC';
import { useLawVirtualization } from '../hooks/useLawVirtualization';
import { combineTitlesInDOM } from '../utils/htmlUtils';
import { TableOfContents } from '../components/StudyScreen/TableOfContents';
import { ArticlePopup } from '../components/StudyScreen/ArticlePopup';
import { AnnotationModal } from '../components/StudyScreen/AnnotationModal';
import { getAllLeis, LeiMeta } from "../data/leis";
import { improveFormatting } from "../utils";
import "../styles/lei.css";
import { ReapplyHighlights } from "../components/ReapplyHighlights";
import { Spinner } from "../components/ui/Spinner";
import { VirtualLeiHandle } from "../components/VirtualLei";
import { useHierarchicalNavToggle } from "../hooks/useHierarchicalNavToggle";
import { HierarchicalNavigation } from "../components/StudyScreen/HierarchicalNavigation";
import { HighlightController } from "../components/StudyScreen/HighlightController";
import { LocalLawView } from "../components/StudyScreen/LocalLawView";
import { useHighlightMenu } from "../hooks/useHighlightMenu";
import { auth } from "../firebase";
import { HighlightMenu } from "../components/StudyScreen/HighlightMenu";
import { HighlightHealthMonitor } from "../components/StudyScreen/HighlightHealthMonitor";

import Modal from "../components/ui/Modal";
import { ProfileScreen } from "./ProfileScreen";

interface SerializedRange {
  startContainerPath: number[];
  startOffset: number;
  endContainerPath: number[];
  endOffset: number;
  text: string;
}

const CORES = ["#00FFFF", "#00FF7F", "#FF00FF", "#FFD700"];
const LIMITE_GRIFOS = 10;

export function StudyScreen() {
  const { leiId } = useParams<{ leiId: string }>();
  const navigate = useNavigate();

  const leiMeta: LeiMeta | undefined = getAllLeis().find((l) => l.id === leiId);

  const [corAtiva, setCorAtiva] = useState(CORES[0]);
  const {
    highlights,
    addHighlight,
    removeHighlight,
    migrateHighlightsIfNeeded,
    validateHighlightsIntegrity,
    getHighlightsHealthReport,
    cleanupOrphanedHighlightsManual,
    isLoading: highlightsLoading,
    isAuthenticated
  } = useHighlights(leiId || "desconhecida");
  const virtualRef = useRef<VirtualLeiHandle>(null);

  const grifosRemaining = useMemo(() => Math.max(0, LIMITE_GRIFOS - highlights.length), [highlights.length]);

  const {
    creditsLeft,
    showPaywall,
    handlePaywallDismiss,
    consumeCredit,
  } = useGuestMode(grifosRemaining);



  const leiRef = useRef<HTMLDivElement | null>(null); // container do header + demais
  const leiContainerRef = useRef<HTMLDivElement | null>(null); // div com id="lei-container"
  const { aumentar, diminuir } = useFontSize(leiRef);

  // Determina se deve usar renderização remota ou local
  const isCustomLaw = leiMeta?.categoria === 'Personalizada';

  // Leis customizadas também precisam buscar conteúdo remoto, mas usarão renderização local
  const { htmlRemoto, erroRemoto, carregando, refreshLaw } = useRemoteLaw(leiId, leiMeta);

  const texto = (leiMeta && (leiMeta.texto || "")) || "Lei não encontrada.";
  const { visibleArtigos, handleScroll, firstChunkIndex } = useChunkedRenderer(texto);

  // Desabilita virtualização para leis customizadas para evitar conflitos de scroll
  useLawVirtualization(isCustomLaw ? null : htmlRemoto, 'lei-container', [highlights]);

  // Combinar títulos separados após o HTML ser carregado (só para leis não customizadas)
  useEffect(() => {
    if (htmlRemoto && leiContainerRef.current && !isCustomLaw) {
      // Usar requestAnimationFrame para garantir que o DOM foi atualizado
      // sem delay desnecessário
      const frame = requestAnimationFrame(() => {
        combineTitlesInDOM(leiContainerRef.current!);
      });

      return () => cancelAnimationFrame(frame);
    }
  }, [htmlRemoto, isCustomLaw]);

  // Para leis customizadas, desabilita useHeaderContext para evitar conflitos de scroll
  const { contextoCabecalho, identLinha } = useHeaderContext(
    isCustomLaw ? { current: null } : leiRef,
    isCustomLaw ? "" : (htmlRemoto || texto)
  );

  // Para leis customizadas, desabilita useLawContext para evitar conflitos de scroll
  const { context: hierarchicalContext, debugInfo } = useLawContext(
    isCustomLaw ? { current: null } : leiRef,
    isCustomLaw ? [] : [htmlRemoto]
  );

  // Hierarchical navigation toggle state
  const { isVisible: isHierarchicalNavVisible, toggle: toggleHierarchicalNav } = useHierarchicalNavToggle();

  useDevice(); // Keep the hook call but don't destructure unused values
  const { serializedSelection, clearSelection } = useTextSelection(leiContainerRef);
  const pendingSelectionRef = useRef<SerializedRange | null>(null);

  // Menu "Grifar" próximo à seleção
  const {
    showGrifoMenu,
    grifoMenuPos,
    setSelectedText,
    setShowGrifoMenu,
  } = useHighlightMenu();

  useEffect(() => {
    if (serializedSelection) {
      pendingSelectionRef.current = serializedSelection;
      setSelectedText(serializedSelection.text, window.getSelection() || undefined);
    } else {
      setShowGrifoMenu(false);
    }
  }, [serializedSelection, setSelectedText, setShowGrifoMenu]);

  function exportarGrifos() {
    const blob = new Blob([JSON.stringify(highlights, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `grifos-${leiId}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  async function importarGrifos(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      if (!Array.isArray(parsed)) throw new Error("Arquivo inválido");
      for (const h of highlights) await removeHighlight(h.id);
      for (const h of parsed) {
        const { color, text = "", article, startContainerPath, startOffset, endContainerPath, endOffset } = h;
        if (startContainerPath && endContainerPath) {
          await addHighlight({ color, text, article, startContainerPath, startOffset, endContainerPath, endOffset });
        }
      }
      toast.success("Grifos importados!");
    } catch (err: any) {
      toast.error(err.message || "Falha ao importar");
    } finally {
      e.target.value = "";
    }
  }

  const { popupArticle, popupPosition, closePopup } = useArticlePopup(leiRef, htmlRemoto);

  const {
    dialogArt,
    loadingExp,
    loadingJur,
    dialogExp,
    dialogJur,
    openDialog,
    closeDialog,
    handleExplain,
    handleExplainWithExample,
    handleJurisprudence,
  } = useAIDialog(consumeCredit);

  // Sistema de anotações
  const {
    annotations,
    getAnnotation,
    saveAnnotation,
    generateWithAI,
    hasAnnotation,
  } = useAnnotations(leiId);

  // Estado do modal de anotações
  const [annotationModal, setAnnotationModal] = useState<{
    isOpen: boolean;
    articleId: string;
    articleText: string;
  }>({
    isOpen: false,
    articleId: '',
    articleText: '',
  });



  // Manipula clique nos artigos
  const handleArticleClick = (articleId: string, articleText: string) => {
    setAnnotationModal({
      isOpen: true,
      articleId,
      articleText,
    });
  };

  // Hook para detectar cliques nos artigos
  useArticleClick({
    containerRef: leiContainerRef,
    onArticleClick: handleArticleClick,
    dependency: htmlRemoto,
  });



  // Hook para mostrar indicadores visuais de anotações
  useAnnotationIndicators({
    containerRef: leiContainerRef,
    annotations,
    dependency: htmlRemoto,
  });

  // Funções para o modal de anotações
  const handleSaveAnnotation = async (data: any) => {
    await saveAnnotation(annotationModal.articleId, data);
  };

  const handleGenerateAI = async (options: any) => {
    return await generateWithAI(annotationModal.articleText, options);
  };

  const closeAnnotationModal = () => {
    setAnnotationModal({
      isOpen: false,
      articleId: '',
      articleText: '',
    });
  };

  function formatIaText(text: string): string {
    const lines = text.split(/\r?\n/);
    let html = "";
    let inList = false;
    const pushCloseList = () => { if (inList) { html += '</ul>'; inList = false; } };
    for (const raw of lines) {
      const line = raw.trim();
      if (!line) {
        pushCloseList();
        html += '<br/>';
        continue;
      }
      if (/^(?:[-*]|\d+\.)\s+/.test(line)) {
        if (!inList) { html += '<ul>'; inList = true; }
        const item = line.replace(/^(?:[-*]|\d+\.)\s+/, '');
        html += `<li>${item}</li>`;
      } else {
        pushCloseList();
        html += `<p>${line}</p>`;
      }
    }
    pushCloseList();
    return html;
  }

  // Source detection state
  const [lawSource, setLawSource] = useState<'local' | 'remote' | null>(null);
  const [sourceLogged, setSourceLogged] = useState(false);

  // Profile modal state
  const [showProfileModal, setShowProfileModal] = useState(false);

  // Handle law refresh
  const handleRefreshLaw = () => {
    if (!refreshLaw) return;
    try {
      refreshLaw();
      toast.success('Lei atualizada com sucesso!');
      // Reset source logging to show new source
      setSourceLogged(false);
    } catch (error: any) {
      toast.error(error.message || 'Erro ao atualizar lei');
    }
  };

  // Detect and log law source only once
  useEffect(() => {
    if (sourceLogged) return;

    const currentSource = htmlRemoto ? 'remote' : 'local';
    if (lawSource !== currentSource) {
      setLawSource(currentSource);
    }

    // Only log when we have a definitive source and haven't logged yet
    if ((htmlRemoto !== null || leiMeta?.texto) && !sourceLogged) {
      const sourceText = htmlRemoto ? 'remota (htmlRemoto)' : 'local (virtualizada)';
      console.info('[StudyScreen] Fonte da lei:', sourceText);
      setSourceLogged(true);
    }
  }, [htmlRemoto, leiMeta?.texto, lawSource, sourceLogged]);

  // Migra highlights quando lei remota é carregada (só para leis não customizadas)
  useEffect(() => {
    if (!htmlRemoto || !leiContainerRef.current || highlights.length === 0 || isCustomLaw) return;

    const container = leiContainerRef.current;

    // Executa migração após um pequeno delay para garantir que o DOM foi renderizado
    const timeoutId = setTimeout(async () => {
      try {
        await migrateHighlightsIfNeeded(container, htmlRemoto, 'current');
      } catch (error) {
        console.error('[StudyScreen] Erro durante migração de highlights:', error);
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [htmlRemoto, highlights.length, migrateHighlightsIfNeeded, isCustomLaw]);

  const { tocItems } = useTOC(isCustomLaw ? null : htmlRemoto);
  const handleTocSelect = (id: string) => {
    // Para leis customizadas, não faz scroll automático para evitar conflitos
    if (isCustomLaw) return;

    const el = document.getElementById(id);
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };



  const handleConfirmGrifo = () => {
    const sel = pendingSelectionRef.current;
    if (sel) {
      const container = leiContainerRef.current;
      if (!container) return;

      // Valida a seleção atual antes de criar o grifo
      const currentSelection = window.getSelection();
      if (currentSelection) {
        const validation = validateSelection(currentSelection, container);
        if (!validation.isValid) {
          toast.error(validation.reason || 'Seleção inválida');
          pendingSelectionRef.current = null;
          clearSelection();
          return;
        }
      }

      const startNode = getNodeFromPath(sel.startContainerPath, container);
      const articleEl = startNode?.parentElement?.closest('[id^="art"]');
      const articleId = articleEl?.id || '';

      // Adiciona o grifo sem marcação temporária - deixa o ReapplyHighlights fazer o trabalho
      addHighlight({
        ...sel,
        color: corAtiva,
        article: articleId,
      }, container, htmlRemoto || undefined).then(() => {
        console.info('Grifado!');
        toast.success('Grifado!');
        pendingSelectionRef.current = null;
        clearSelection();
      }).catch((error) => {
        console.error('Erro ao adicionar grifo:', error);
        toast.error(error instanceof Error ? error.message : 'Erro ao salvar grifo');
      });
    }
  };

  const localTocItems = useMemo(() => {
    return visibleArtigos.map((art: string) => {
      const idMatch = art.match(/^(?:Art(?:igo|\.)\s)(\d+)/i);
      const id = idMatch && idMatch[1] ? `art${idMatch[1]}` : art.substring(0, 10);
      const titleMatch = art.match(/^(Artigo \d+|Art\. \d+|CAPÍTULO [IVXLCDM]+|TÍTULO [IVXLCDM]+)/i);
      return {
        id: id,
        title: titleMatch ? titleMatch[0] : art.substring(0, 40) + '...',
      };
    });
  }, [visibleArtigos]);

  if (!leiMeta) {
    return <div>Lei não encontrada.</div>;
  }

  // Leis customizadas usam renderização remota, mas sem hooks conflitantes
  // Todas as outras leis usam renderização remota normal
  const shouldUseRemoteRendering = leiMeta.url;

  if (shouldUseRemoteRendering) {
    return (
      <div className="p-4" ref={leiRef} style={{ fontSize: "var(--fs)" } as React.CSSProperties}>
        <header className="flex items-start gap-3 flex-wrap mb-4">
          <button onClick={() => navigate('..')} className="p-2 rounded hover:bg-white/10" aria-label="Voltar">
            <ChevronLeft />
          </button>
          <div className="flex-1 min-w-0">
            <h2 className="font-semibold text-xl capitalize leading-snug break-words">
              {leiMeta.nome}
            </h2>
            {identLinha && (
              <p className="text-[11px] text-blue-300 leading-snug break-words">
                {identLinha}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {auth?.currentUser && (
              <button onClick={() => setShowProfileModal(true)} className="text-xs px-2 py-1 rounded bg-primary text-primary-foreground shadow-lg">Perfil</button>
            )}
            {leiMeta.url && (
              <button
                onClick={handleRefreshLaw}
                className="p-2 rounded hover:bg-white/10 text-sm"
                title="Atualizar lei (máximo 1x por dia)"
                aria-label="Atualizar lei"
              >
                ↻
              </button>
            )}
            <HighlightController activeColor={corAtiva} onColorChange={setCorAtiva} />
            <button onClick={diminuir} className="p-2 rounded hover:bg-white/10" aria-label="Diminuir texto">A−</button>
            <button onClick={aumentar} className="p-2 rounded hover:bg-white/10" aria-label="Aumentar texto">A+</button>
            <ThemeToggle />
            {!isHierarchicalNavVisible && (hierarchicalContext || contextoCabecalho) && (
              <button
                onClick={toggleHierarchicalNav}
                className="p-2 rounded hover:bg-white/10 text-sm"
                title="Mostrar navegação hierárquica na tela"
                aria-label="Mostrar navegação hierárquica"
              >
                <span className="text-xs">⋯</span>
              </button>
            )}
          </div>
        </header>

        {/* Enhanced Hierarchical Navigation for remote laws */}
        {(hierarchicalContext || contextoCabecalho) && isHierarchicalNavVisible && (
          <HierarchicalNavigation
            context={hierarchicalContext || contextoCabecalho || ''}
            debugInfo={debugInfo}
            onHide={toggleHierarchicalNav}
          />
        )}



        {carregando && <Spinner message="Carregando conteúdo da lei…" />}

        {erroRemoto && (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <div className="bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg p-6 max-w-md">
              <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                Erro ao carregar lei
              </h3>
              <p className="text-red-700 dark:text-red-300 mb-4">
                {erroRemoto}
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Tentar novamente
              </button>
            </div>
          </div>
        )}

        {htmlRemoto && !erroRemoto && (
          <div
            id="lei-container"
            ref={leiContainerRef}
            className="prose max-w-none select-text dark:prose-invert"
            dangerouslySetInnerHTML={{ __html: htmlRemoto || '' }}
          />
        )}

        <ReapplyHighlights
          highlights={highlights}
          containerId="lei-container"
          getNodeFromPath={getNodeFromPath}
          onRemove={(id) => removeHighlight(id)}
        />

        {popupArticle && (
          <ArticlePopup
            article={popupArticle}
            position={popupPosition}
            onClose={closePopup}
          />
        )}

        {dialogArt && (
          <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center" onClick={closeDialog}>
            <div className="bg-neutral-800 p-4 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
              <h3 className="text-lg font-bold mb-2 text-primary truncate">{dialogArt.title}</h3>
              <div className="text-sm opacity-80 overflow-y-auto pr-2 flex-1" dangerouslySetInnerHTML={{ __html: improveFormatting(dialogArt.texto) }} />
              <div className="flex flex-wrap gap-2 mt-4 pt-2 border-t border-white/10">
                <button disabled={loadingExp} onClick={handleExplain} className="text-xs px-2 py-1 rounded bg-primary/20 hover:bg-primary/30 disabled:opacity-50">
                  {loadingExp ? '...' : 'Explique'}
                </button>
                <button disabled={loadingExp} onClick={handleExplainWithExample} className="text-xs px-2 py-1 rounded bg-primary/20 hover:bg-primary/30 disabled:opacity-50">
                  {loadingExp ? '...' : 'Com exemplo'}
                </button>
                <button disabled={loadingJur} onClick={handleJurisprudence} className="text-xs px-2 py-1 rounded bg-primary/20 hover:bg-primary/30 disabled:opacity-50">
                  {loadingJur ? '...' : 'Jurisprudência'}
                </button>
              </div>
              {dialogExp && <div className="mt-2 text-sm border-t border-dashed border-white/20 pt-2" dangerouslySetInnerHTML={{ __html: formatIaText(dialogExp) }} />}
              {dialogJur && <div className="mt-2 text-sm border-t border-dashed border-white/20 pt-2" dangerouslySetInnerHTML={{ __html: formatIaText(dialogJur) }} />}
            </div>
          </div>
        )}

        {/* TableOfContents desabilitado para leis customizadas para evitar conflitos */}
        {!isCustomLaw && <TableOfContents tocItems={tocItems} onSelect={handleTocSelect} />}


        {/* Botão Grifar para desktop/mobile próximo à seleção */}
        <HighlightMenu
          show={showGrifoMenu}
          position={grifoMenuPos}
          onConfirm={() => {
            handleConfirmGrifo();
            setShowGrifoMenu(false);
          }}
        />

        {/* Annotation Modal */}
        <AnnotationModal
          isOpen={annotationModal.isOpen}
          onClose={closeAnnotationModal}
          articleId={annotationModal.articleId}
          articleText={annotationModal.articleText}
          leiId={leiId}
          annotation={annotations.get(annotationModal.articleId)}
          onSave={handleSaveAnnotation}
          onGenerateAI={handleGenerateAI}
        />
      </div>
    );
  }

  return (
    <>
    <LocalLawView
       containerRef={leiContainerRef}
      leiMeta={leiMeta}
      navigate={navigate}
      corAtiva={corAtiva}
      setCorAtiva={setCorAtiva}
      diminuir={diminuir}
      aumentar={aumentar}
      exportarGrifos={exportarGrifos}
      importarGrifos={importarGrifos}

      removeHighlight={removeHighlight}
      virtualRef={virtualRef}
      highlights={highlights}
      visibleArtigos={visibleArtigos.map((art, index) => ({ id: `art${firstChunkIndex + index + 1}`, texto: art }))}
      handleScroll={handleScroll}
      tocItems={localTocItems}
      showPaywall={showPaywall}
      handlePaywallDismiss={handlePaywallDismiss}
      creditsLeft={creditsLeft}
      dialogArt={dialogArt}
      closeDialog={closeDialog}
      loadingExp={loadingExp}
      handleExplain={handleExplain}
      handleExplainWithExample={handleExplainWithExample}
      loadingJur={loadingJur}
      handleJurisprudence={handleJurisprudence}
      dialogExp={dialogExp}
      dialogJur={dialogJur}
      formatIaText={formatIaText}

    />





    {/* Highlight Health Monitor */}
    <HighlightHealthMonitor
      container={leiContainerRef.current}
      getHealthReport={getHighlightsHealthReport}
      validateIntegrity={validateHighlightsIntegrity}
      cleanupOrphaned={cleanupOrphanedHighlightsManual}
      migrateHighlights={migrateHighlightsIfNeeded}
      lawContent={htmlRemoto || undefined}
      lawVersion="current"
    />

    {/* Profile Modal */}
    <Modal isOpen={showProfileModal} onClose={() => setShowProfileModal(false)} size="lg">
      <ProfileScreen minimal={true} onClose={() => setShowProfileModal(false)} />
    </Modal>


    </>
  );
}
